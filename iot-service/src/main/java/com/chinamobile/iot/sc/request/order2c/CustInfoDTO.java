package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/11/3 17:17
 * @Description: 客户信息
 */
@Data
public class CustInfoDTO {
    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户UserID
     * 与客户编码（custCode）搭配使用，传在商城注册的个人客户、集团客户的UserID信息，省侧集团客户不传，代客下单场景下不传
     */
    private String custUserID;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 个人客户所属省份。
     */
    private String beId;
    /**
     * 个人客户所属归属地市编码
     */
    private String location;
    /**
     * 个人客户所属归属区县
     */
    private String regionID;
}
