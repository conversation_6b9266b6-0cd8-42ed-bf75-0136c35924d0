package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

/**
 * created by liuxiang on 2024/3/5 09:11
 * 订单渠道商信息
 */
@Data
public class AgentInfoDTO {

    //渠道商全称
    private String agentName;

    //渠道商编码
    private String agentNumber;

    //渠道商手机号,加密传输
    private String agentPhone;

    /**
     * 与渠道商手机号（agentPhone）搭配使用，传渠道商的UserID信息，省侧渠道商不传
     * */
    private String agentUserID;
}
