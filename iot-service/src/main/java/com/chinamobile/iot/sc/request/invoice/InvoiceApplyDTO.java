package com.chinamobile.iot.sc.request.invoice;

import lombok.Data;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: InvoiceApplyDTO
 * @description: 开发票申请-IOT商城请求
 * @author: zyj
 * @create: 2021/11/30 14:42
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class InvoiceApplyDTO {
    /**
     * 请求流水号, 发票业务唯一标识（必填）
     */
    private String orderSeq;
    /**
     * 业务订单流水号, 销售业务的唯一标识（必填）
     */
    private String orderId;
    /**
     * 请求发票开具操作时间, YYYYMMDD24HHMISS（必填）
     */
    private String printDate;
    /**
     * 发票标记, 0：专票；1：普票；（必填）
     * 个人客户只能开具普票，不能开具专票。
     * 固定填：1;
     */
    private String frank;
    /**
     * 发票金额
     */
    private Long voucherSum;

    /**
     * 发票抬头信息
     */
    private TaxpayerInfoDTO taxpayerInfo;

}
