package com.chinamobile.iot.sc.request.invoice;

import lombok.Data;

import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.request
 * @ClassName: IotInvoiceBackRequest
 * @description: 电子发票开具结果反馈请求
 * @author: zyj
 * @create: 2021/12/8 14:40
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class IotInvoiceBackRequest {

    private String orderSeq;
    /**
     * 开具结果：
     * 1：已开具
     * 0：未开具
     * -1：开具失败
     */
    private String result;
    /**
     * 失败的具体原因，如航信返回的错误信息
     */
    private String errorDesc;

    private List<PrintInfo> printInfo;

    @Data
    public static class PrintInfo{
        private String voucherID;
        private String voucherNum;
        private String voucherSum;
        private String voucherFile;
    }
}
