package com.chinamobile.iot.sc.request.address;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @package: com.chinamobile.iot.sc.request.address
 * @ClassName: Request4ParAddrAdd
 * @description: 新增合作伙伴地址
 * @author: zyj
 * @create: 2021/12/15 14:23
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Request4ParAddrAdd {

    @Size(min = 1, max = 100, message = "退换货地址，最长不超过100个字符")
    @NotBlank(message = "退换货地址必填！")
    private String returnAddress;
    @NotBlank(message = "联系人姓名必填！")
    private String contactName;
    @NotBlank(message = "手机号必填！")
    private String contactPhone;
    @NotNull(message = "默认地址必填！")
    private Boolean isDefault = false;
//    @NotBlank(message = "合作伙伴id不能为空！")
    private String partnerId;

    /**
     * 主合作伙伴时  0：新增自己的地址  1：新增从合作伙伴地址
     */
    private String operate;
    /**
     * 从账号 联系人姓名
     */
    private String partnerSlaveName;

    /**
     * 从账号 id
     */
    private String partnerSlaveId;

}
