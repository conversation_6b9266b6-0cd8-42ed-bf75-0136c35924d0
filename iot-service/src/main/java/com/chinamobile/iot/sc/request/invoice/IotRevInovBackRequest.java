package com.chinamobile.iot.sc.request.invoice;

import lombok.Data;

import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: IotRevInovBackRequest
 * @description: 冲红发票结果反馈请求-IOT
 * @author: zyj
 * @create: 2021/12/30 16:12
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class IotRevInovBackRequest {

    private String beId;

    private String orderSeq;

    private String operType;

    private String customerNumber;

    private String result;

    private String errorDesc;

    private List<VoucherInfo> voucherInfo;

    @Data
    public static class VoucherInfo{
        private String voucherID;
        private String voucherNum;
        private Long voucherSum;
        private String creditNoteID;
        private String creditNoteNum;
        private String billingDate;
        private String remark;
    }
}
